[1.080s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[1.081s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=2, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x759f04671df0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x759f04671820>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x759f04671820>>, mixin_verb=('build',))
[1.330s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.331s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.331s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.331s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.331s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.332s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.332s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/articubot_one/dev_ws'
[1.332s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.333s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.333s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.334s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.334s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.334s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.334s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.335s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.335s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.503s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.503s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.503s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.504s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.504s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.504s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.505s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.505s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.506s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.507s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.507s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.508s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.508s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.508s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.509s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.509s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.510s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.511s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.511s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.511s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.512s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.526s] DEBUG:colcon.colcon_core.package_identification:Package 'src' with type 'ros.ament_cmake' and name 'articubot_one'
[1.526s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.628s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.628s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.642s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.645s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.645s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.646s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.646s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.646s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.646s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.649s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.650s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable AMENT_PREFIX_PATH doesn't exist
[1.652s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.653s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.653s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.654s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.654s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.655s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.655s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.656s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.656s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[1.667s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 7 installed packages in /home/<USER>/Roomba/slam_dev_ws/install
[1.680s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 364 installed packages in /opt/ros/jazzy
[1.686s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[2.112s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'cmake_args' from command line to 'None'
[2.112s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'cmake_target' from command line to 'None'
[2.112s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[2.112s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'cmake_clean_cache' from command line to 'False'
[2.112s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'cmake_clean_first' from command line to 'False'
[2.113s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'cmake_force_configure' from command line to 'False'
[2.113s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'ament_cmake_args' from command line to 'None'
[2.113s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'catkin_cmake_args' from command line to 'None'
[2.113s] Level 5:colcon.colcon_core.verb:set package 'articubot_one' build argument 'catkin_skip_building_tests' from command line to 'False'
[2.113s] DEBUG:colcon.colcon_core.verb:Building package 'articubot_one' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/articubot_one/dev_ws/build/articubot_one', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/articubot_one/dev_ws/install/articubot_one', 'merge_install': False, 'path': '/home/<USER>/articubot_one/dev_ws/src', 'symlink_install': True, 'test_result_base': None}
[2.114s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[2.126s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[2.127s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/articubot_one/dev_ws/src' with build type 'ament_cmake'
[2.132s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/articubot_one/dev_ws/src'
[2.182s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[2.183s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.183s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.290s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/articubot_one/dev_ws/src -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/articubot_one/dev_ws/install/articubot_one
[10.634s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/articubot_one/dev_ws/src -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/articubot_one/dev_ws/install/articubot_one
[10.658s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/articubot_one/dev_ws/build/articubot_one -- -j2 -l2
[10.958s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/articubot_one/dev_ws/build/articubot_one -- -j2 -l2
[11.051s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/articubot_one/dev_ws/build/articubot_one
[14.834s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(articubot_one)
[14.836s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/articubot_one/dev_ws/build/articubot_one
[14.870s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one' for CMake module files
[14.874s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one' for CMake config files
[14.876s] Level 1:colcon.colcon_core.shell:create_environment_hook('articubot_one', 'cmake_prefix_path')
[14.877s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/hook/cmake_prefix_path.ps1'
[14.880s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/hook/cmake_prefix_path.dsv'
[14.882s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/hook/cmake_prefix_path.sh'
[14.890s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/bin'
[14.890s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/lib/pkgconfig/articubot_one.pc'
[14.891s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/lib/python3.12/site-packages'
[14.892s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/bin'
[14.894s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.ps1'
[14.898s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.dsv'
[14.901s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.sh'
[14.904s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.bash'
[14.914s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.zsh'
[14.924s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/colcon-core/packages/articubot_one)
[14.936s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(articubot_one)
[14.937s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one' for CMake module files
[14.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one' for CMake config files
[14.943s] Level 1:colcon.colcon_core.shell:create_environment_hook('articubot_one', 'cmake_prefix_path')
[14.944s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/hook/cmake_prefix_path.ps1'
[14.946s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/hook/cmake_prefix_path.dsv'
[14.948s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/hook/cmake_prefix_path.sh'
[14.952s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/bin'
[14.956s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/lib/pkgconfig/articubot_one.pc'
[14.957s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/lib/python3.12/site-packages'
[14.962s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/articubot_one/dev_ws/install/articubot_one/bin'
[14.964s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.ps1'
[14.969s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.dsv'
[14.974s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.sh'
[14.978s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.bash'
[14.981s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.zsh'
[14.985s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/articubot_one/dev_ws/install/articubot_one/share/colcon-core/packages/articubot_one)
[14.987s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[14.987s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[14.990s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[14.990s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[15.093s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[15.094s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[15.094s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[15.388s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[15.389s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/articubot_one/dev_ws/install/local_setup.ps1'
[15.396s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/articubot_one/dev_ws/install/_local_setup_util_ps1.py'
[15.403s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/articubot_one/dev_ws/install/setup.ps1'
[15.412s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/articubot_one/dev_ws/install/local_setup.sh'
[15.416s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/articubot_one/dev_ws/install/_local_setup_util_sh.py'
[15.420s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/articubot_one/dev_ws/install/setup.sh'
[15.429s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/articubot_one/dev_ws/install/local_setup.bash'
[15.433s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/articubot_one/dev_ws/install/setup.bash'
[15.442s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/articubot_one/dev_ws/install/local_setup.zsh'
[15.446s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/articubot_one/dev_ws/install/setup.zsh'
