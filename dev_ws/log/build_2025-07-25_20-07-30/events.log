[0.000000] (-) TimerEvent: {}
[0.013398] (articubot_one) JobQueued: {'identifier': 'articubot_one', 'dependencies': OrderedDict()}
[0.013683] (articubot_one) JobStarted: {'identifier': 'articubot_one'}
[0.103594] (-) TimerEvent: {}
[0.140204] (articubot_one) JobProgress: {'identifier': 'articubot_one', 'progress': 'cmake'}
[0.154058] (articubot_one) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/articubot_one/dev_ws/src', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/articubot_one/dev_ws/install/articubot_one'], 'cwd': '/home/<USER>/articubot_one/dev_ws/build/articubot_one', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'smtuser', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/articubot_one/dev_ws/src', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'ROS_IP': '*************', 'SYSTEMD_EXEC_PID': '2836', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1001/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'smtuser', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/pkgconfig', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1001.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'smtuser', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/sparky-HP-Laptop-15-bs0xx:@/tmp/.ICE-unix/2801,unix/sparky-HP-Laptop-15-bs0xx:/tmp/.ICE-unix/2801', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/6f8d9039_cbcc_43d3_b2d5_f1286bc04dae', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1001', 'DISPLAY': ':0', 'LD_PRELOAD': '', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1001/.mutter-Xwaylandauth.5HCV92', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.134', 'SSH_AUTH_SOCK': '/run/user/1001/keyring/ssh', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/articubot_one/dev_ws/build/articubot_one', 'QT_QPA_PLATFORM': 'xcb', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.206411] (-) TimerEvent: {}
[0.314700] (-) TimerEvent: {}
[0.417421] (-) TimerEvent: {}
[0.527784] (-) TimerEvent: {}
[0.630421] (-) TimerEvent: {}
[0.735074] (-) TimerEvent: {}
[0.837564] (-) TimerEvent: {}
[0.944631] (-) TimerEvent: {}
[0.982439] (articubot_one) StdoutLine: {'line': b'-- The C compiler identification is GNU 13.3.0\n'}
[1.044818] (-) TimerEvent: {}
[1.149243] (-) TimerEvent: {}
[1.266437] (-) TimerEvent: {}
[1.372406] (-) TimerEvent: {}
[1.478999] (-) TimerEvent: {}
[1.558037] (articubot_one) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 13.3.0\n'}
[1.579546] (-) TimerEvent: {}
[1.680841] (-) TimerEvent: {}
[1.715573] (articubot_one) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[1.783518] (-) TimerEvent: {}
[1.889394] (-) TimerEvent: {}
[1.994272] (-) TimerEvent: {}
[2.095379] (-) TimerEvent: {}
[2.199218] (-) TimerEvent: {}
[2.310795] (-) TimerEvent: {}
[2.419661] (-) TimerEvent: {}
[2.525032] (-) TimerEvent: {}
[2.629412] (-) TimerEvent: {}
[2.632713] (articubot_one) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[2.731060] (-) TimerEvent: {}
[2.756282] (articubot_one) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[2.764109] (articubot_one) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[2.768446] (articubot_one) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[2.833827] (-) TimerEvent: {}
[2.848756] (articubot_one) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[2.934277] (-) TimerEvent: {}
[3.038637] (-) TimerEvent: {}
[3.158438] (-) TimerEvent: {}
[3.267066] (-) TimerEvent: {}
[3.368677] (-) TimerEvent: {}
[3.471601] (-) TimerEvent: {}
[3.573176] (-) TimerEvent: {}
[3.683096] (-) TimerEvent: {}
[3.786493] (-) TimerEvent: {}
[3.842931] (articubot_one) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[3.886916] (-) TimerEvent: {}
[3.916139] (articubot_one) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[3.916924] (articubot_one) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[3.920153] (articubot_one) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[3.979079] (articubot_one) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[3.987046] (-) TimerEvent: {}
[4.088575] (-) TimerEvent: {}
[4.194364] (-) TimerEvent: {}
[4.309157] (-) TimerEvent: {}
[4.412168] (-) TimerEvent: {}
[4.519168] (-) TimerEvent: {}
[4.620771] (-) TimerEvent: {}
[4.726023] (-) TimerEvent: {}
[4.832087] (-) TimerEvent: {}
[4.933049] (-) TimerEvent: {}
[5.035368] (-) TimerEvent: {}
[5.141081] (-) TimerEvent: {}
[5.243522] (-) TimerEvent: {}
[5.349041] (-) TimerEvent: {}
[5.454947] (-) TimerEvent: {}
[5.559019] (-) TimerEvent: {}
[5.661259] (-) TimerEvent: {}
[5.723771] (articubot_one) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter \n'}
[5.763017] (-) TimerEvent: {}
[5.863962] (-) TimerEvent: {}
[5.964782] (-) TimerEvent: {}
[6.069075] (-) TimerEvent: {}
[6.172845] (-) TimerEvent: {}
[6.275218] (articubot_one) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[6.275994] (-) TimerEvent: {}
[6.380134] (-) TimerEvent: {}
[6.488019] (-) TimerEvent: {}
[6.598104] (-) TimerEvent: {}
[6.704252] (-) TimerEvent: {}
[6.805659] (-) TimerEvent: {}
[6.892136] (articubot_one) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[6.906049] (-) TimerEvent: {}
[7.007828] (-) TimerEvent: {}
[7.109047] (-) TimerEvent: {}
[7.211242] (-) TimerEvent: {}
[7.318275] (-) TimerEvent: {}
[7.420701] (-) TimerEvent: {}
[7.528040] (-) TimerEvent: {}
[7.631478] (-) TimerEvent: {}
[7.734114] (-) TimerEvent: {}
[7.839980] (-) TimerEvent: {}
[7.941024] (-) TimerEvent: {}
[8.044037] (-) TimerEvent: {}
[8.145125] (-) TimerEvent: {}
[8.192025] (articubot_one) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[8.246085] (-) TimerEvent: {}
[8.263211] (articubot_one) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[8.264174] (articubot_one) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[8.292041] (articubot_one) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[8.306468] (articubot_one) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[8.346302] (-) TimerEvent: {}
[8.349224] (articubot_one) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[8.382950] (articubot_one) StdoutLine: {'line': b'-- Configuring done (8.1s)\n'}
[8.429838] (articubot_one) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[8.446030] (articubot_one) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/articubot_one/dev_ws/build/articubot_one\n'}
[8.449167] (-) TimerEvent: {}
[8.514150] (articubot_one) CommandEnded: {'returncode': 0}
[8.522015] (articubot_one) JobProgress: {'identifier': 'articubot_one', 'progress': 'build'}
[8.522414] (articubot_one) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/articubot_one/dev_ws/build/articubot_one', '--', '-j2', '-l2'], 'cwd': '/home/<USER>/articubot_one/dev_ws/build/articubot_one', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'smtuser', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/articubot_one/dev_ws/src', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'ROS_IP': '*************', 'SYSTEMD_EXEC_PID': '2836', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1001/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'smtuser', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/pkgconfig', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1001.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'smtuser', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/sparky-HP-Laptop-15-bs0xx:@/tmp/.ICE-unix/2801,unix/sparky-HP-Laptop-15-bs0xx:/tmp/.ICE-unix/2801', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/6f8d9039_cbcc_43d3_b2d5_f1286bc04dae', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1001', 'DISPLAY': ':0', 'LD_PRELOAD': '', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1001/.mutter-Xwaylandauth.5HCV92', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.134', 'SSH_AUTH_SOCK': '/run/user/1001/keyring/ssh', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/articubot_one/dev_ws/build/articubot_one', 'QT_QPA_PLATFORM': 'xcb', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[8.549572] (-) TimerEvent: {}
[8.654404] (-) TimerEvent: {}
[8.762503] (-) TimerEvent: {}
[8.838136] (articubot_one) CommandEnded: {'returncode': 0}
[8.844015] (articubot_one) JobProgress: {'identifier': 'articubot_one', 'progress': 'install'}
[8.862715] (-) TimerEvent: {}
[8.928162] (articubot_one) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/articubot_one/dev_ws/build/articubot_one'], 'cwd': '/home/<USER>/articubot_one/dev_ws/build/articubot_one', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'USER': 'smtuser', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox/lib:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/articubot_one/dev_ws/src', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'ROS_IP': '*************', 'SYSTEMD_EXEC_PID': '2836', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1001/bus', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'smtuser', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Roomba/slam_dev_ws/install/libcreate/lib/pkgconfig', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1001.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'smtuser', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/sparky-HP-Laptop-15-bs0xx:@/tmp/.ICE-unix/2801,unix/sparky-HP-Laptop-15-bs0xx:/tmp/.ICE-unix/2801', 'XDG_MENU_PREFIX': 'gnome-', 'GNOME_TERMINAL_SCREEN': '/org/gnome/Terminal/screen/6f8d9039_cbcc_43d3_b2d5_f1286bc04dae', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1001', 'DISPLAY': ':0', 'LD_PRELOAD': '', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1001/.mutter-Xwaylandauth.5HCV92', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'GNOME_TERMINAL_SERVICE': ':1.134', 'SSH_AUTH_SOCK': '/run/user/1001/keyring/ssh', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/articubot_one/dev_ws/build/articubot_one', 'QT_QPA_PLATFORM': 'xcb', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup/lib/python3.12/site-packages:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/home/<USER>/Roomba/slam_dev_ws/install/rplidar_ros:/home/<USER>/Roomba/slam_dev_ws/install/create_robot:/home/<USER>/Roomba/slam_dev_ws/install/create_bringup:/home/<USER>/Roomba/slam_dev_ws/install/create_driver:/home/<USER>/Roomba/slam_dev_ws/install/libcreate:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_sim:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_common_bringup:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_nodes:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_toolbox:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_gz_plugins:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_description:/home/<USER>/Roomba/slam_dev_ws/install/irobot_create_control:/home/<USER>/Roomba/slam_dev_ws/install/create_msgs:/home/<USER>/Roomba/slam_dev_ws/install/create_description:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[8.965094] (-) TimerEvent: {}
[9.028397] (articubot_one) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[9.036022] (articubot_one) StdoutLine: {'line': b'-- Execute custom install script\n'}
[9.051242] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/ball_tracker_params_robot.yaml\n'}
[9.072148] (-) TimerEvent: {}
[9.184502] (-) TimerEvent: {}
[9.219774] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/ball_tracker_params_sim.yaml\n'}
[9.286084] (-) TimerEvent: {}
[9.330474] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/drive_bot.rviz\n'}
[9.387786] (-) TimerEvent: {}
[9.417894] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/empty.yaml\n'}
[9.491238] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/gaz_ros2_ctl_use_sim.yaml\n'}
[9.492558] (-) TimerEvent: {}
[9.559474] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/gz_bridge.yaml\n'}
[9.596986] (-) TimerEvent: {}
[9.653539] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/joystick.yaml\n'}
[9.697229] (-) TimerEvent: {}
[9.738355] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/main.rviz\n'}
[9.799055] (-) TimerEvent: {}
[9.807153] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/map.rviz\n'}
[9.861426] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/mapper_params_online_async.yaml\n'}
[9.897774] (-) TimerEvent: {}
[9.922594] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/my_controllers.yaml\n'}
[9.971227] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/nav2_params.yaml\n'}
[9.998047] (-) TimerEvent: {}
[10.025420] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/twist_mux.yaml\n'}
[10.073326] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/view_bot.rviz\n'}
[10.098329] (-) TimerEvent: {}
[10.129442] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/camera.xacro\n'}
[10.183525] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/depth_camera.xacro\n'}
[10.201030] (-) TimerEvent: {}
[10.247481] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/face.xacro\n'}
[10.302968] (-) TimerEvent: {}
[10.321072] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/gazebo_control.xacro\n'}
[10.406315] (-) TimerEvent: {}
[10.428123] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/inertial_macros.xacro\n'}
[10.506561] (-) TimerEvent: {}
[10.525017] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/lidar.xacro\n'}
[10.599223] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/robot.urdf.xacro\n'}
[10.606731] (-) TimerEvent: {}
[10.675108] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/robot_core.xacro\n'}
[10.707034] (-) TimerEvent: {}
[10.740718] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/ros2_control.xacro\n'}
[10.806289] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/ball_tracker.launch.py\n'}
[10.808264] (-) TimerEvent: {}
[10.852076] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/camera.launch.py\n'}
[10.908832] (-) TimerEvent: {}
[10.924690] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/joystick.launch.py\n'}
[10.976152] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/launch_robot.launch.py\n'}
[11.008818] (-) TimerEvent: {}
[11.029129] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/launch_sim.launch.py\n'}
[11.090520] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/localization_launch.py\n'}
[11.109062] (-) TimerEvent: {}
[11.135520] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/navigation_launch.py\n'}
[11.200134] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/online_async_launch.py\n'}
[11.209305] (-) TimerEvent: {}
[11.258719] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/rplidar.launch.py\n'}
[11.309839] (-) TimerEvent: {}
[11.416951] (-) TimerEvent: {}
[11.463558] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/rsp.launch.py\n'}
[11.519988] (-) TimerEvent: {}
[11.569361] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/worlds/empty.world\n'}
[11.621081] (-) TimerEvent: {}
[11.650411] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/worlds/obstacles.world\n'}
[11.721308] (-) TimerEvent: {}
[11.722732] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/package_run_dependencies/articubot_one\n'}
[11.796637] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/parent_prefix_path/articubot_one\n'}
[11.821736] (-) TimerEvent: {}
[11.854992] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/ament_prefix_path.sh\n'}
[11.907702] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/ament_prefix_path.dsv\n'}
[11.922171] (-) TimerEvent: {}
[11.959682] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/path.sh\n'}
[12.015205] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/path.dsv\n'}
[12.023048] (-) TimerEvent: {}
[12.081458] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.bash\n'}
[12.124115] (-) TimerEvent: {}
[12.211132] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.sh\n'}
[12.226203] (-) TimerEvent: {}
[12.272478] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.zsh\n'}
[12.326735] (-) TimerEvent: {}
[12.332058] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.dsv\n'}
[12.393638] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.dsv\n'}
[12.427980] (-) TimerEvent: {}
[12.448751] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/packages/articubot_one\n'}
[12.505270] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/cmake/articubot_oneConfig.cmake\n'}
[12.529078] (-) TimerEvent: {}
[12.586295] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/cmake/articubot_oneConfig-version.cmake\n'}
[12.631095] (-) TimerEvent: {}
[12.638509] (articubot_one) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.xml\n'}
[12.717234] (articubot_one) CommandEnded: {'returncode': 0}
[12.732044] (-) TimerEvent: {}
[12.834226] (-) TimerEvent: {}
[12.871067] (articubot_one) JobEnded: {'identifier': 'articubot_one', 'rc': 0}
[12.878323] (-) EventReactorShutdown: {}
