[0.156s] Invoking command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/articubot_one/dev_ws/src -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/articubot_one/dev_ws/install/articubot_one
[0.972s] -- The C compiler identification is GNU 13.3.0
[1.546s] -- The CXX compiler identification is GNU 13.3.0
[1.704s] -- Detecting C compiler ABI info
[2.620s] -- Detecting C compiler ABI info - done
[2.747s] -- Check for working C compiler: /usr/bin/cc - skipped
[2.751s] -- Detecting C compile features
[2.755s] -- Detecting C compile features - done
[2.841s] -- Detecting CXX compiler ABI info
[3.831s] -- Detecting CXX compiler AB<PERSON> info - done
[3.903s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[3.904s] -- Detecting CXX compile features
[3.907s] -- Detecting CXX compile features - done
[3.966s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[5.712s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[6.262s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[6.879s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[8.179s] -- Added test 'copyright' to check source files copyright and LICENSE
[8.250s] -- Added test 'flake8' to check Python code syntax and style conventions
[8.251s] -- Configured 'flake8' exclude dirs and/or files: 
[8.279s] -- Added test 'lint_cmake' to check CMake code style
[8.296s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[8.336s] -- Added test 'xmllint' to check XML markup files
[8.370s] -- Configuring done (8.1s)
[8.417s] -- Generating done (0.0s)
[8.433s] -- Build files have been written to: /home/<USER>/articubot_one/dev_ws/build/articubot_one
[8.502s] Invoked command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/articubot_one/dev_ws/src -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/articubot_one/dev_ws/install/articubot_one
[8.521s] Invoking command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/articubot_one/dev_ws/build/articubot_one -- -j2 -l2
[8.827s] Invoked command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/articubot_one/dev_ws/build/articubot_one -- -j2 -l2
[8.917s] Invoking command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/articubot_one/dev_ws/build/articubot_one
[9.015s] -- Install configuration: ""
[9.023s] -- Execute custom install script
[9.038s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/ball_tracker_params_robot.yaml
[9.208s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/ball_tracker_params_sim.yaml
[9.319s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/drive_bot.rviz
[9.405s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/empty.yaml
[9.478s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/gaz_ros2_ctl_use_sim.yaml
[9.546s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/gz_bridge.yaml
[9.640s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/joystick.yaml
[9.725s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/main.rviz
[9.809s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/map.rviz
[9.848s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/mapper_params_online_async.yaml
[9.910s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/my_controllers.yaml
[9.958s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/nav2_params.yaml
[10.012s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/twist_mux.yaml
[10.060s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/view_bot.rviz
[10.116s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/camera.xacro
[10.184s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/depth_camera.xacro
[10.234s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/face.xacro
[10.308s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/gazebo_control.xacro
[10.415s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/inertial_macros.xacro
[10.514s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/lidar.xacro
[10.587s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/robot.urdf.xacro
[10.662s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/robot_core.xacro
[10.729s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/ros2_control.xacro
[10.794s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/ball_tracker.launch.py
[10.841s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/camera.launch.py
[10.915s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/joystick.launch.py
[10.964s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/launch_robot.launch.py
[11.017s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/launch_sim.launch.py
[11.083s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/localization_launch.py
[11.124s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/navigation_launch.py
[11.187s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/online_async_launch.py
[11.249s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/rplidar.launch.py
[11.450s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/rsp.launch.py
[11.557s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/worlds/empty.world
[11.639s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/worlds/obstacles.world
[11.712s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/package_run_dependencies/articubot_one
[11.786s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/parent_prefix_path/articubot_one
[11.842s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/ament_prefix_path.sh
[11.895s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/ament_prefix_path.dsv
[11.948s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/path.sh
[12.005s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/path.dsv
[12.075s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.bash
[12.198s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.sh
[12.260s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.zsh
[12.319s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.dsv
[12.385s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.dsv
[12.443s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/packages/articubot_one
[12.492s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/cmake/articubot_oneConfig.cmake
[12.573s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/cmake/articubot_oneConfig-version.cmake
[12.626s] -- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.xml
[12.705s] Invoked command in '/home/<USER>/articubot_one/dev_ws/build/articubot_one' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/articubot_one/dev_ws/build/articubot_one
