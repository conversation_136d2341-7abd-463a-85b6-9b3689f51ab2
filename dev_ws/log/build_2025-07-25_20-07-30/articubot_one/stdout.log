-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done (8.1s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/articubot_one/dev_ws/build/articubot_one
-- Install configuration: ""
-- Execute custom install script
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/ball_tracker_params_robot.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/ball_tracker_params_sim.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/drive_bot.rviz
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/empty.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/gaz_ros2_ctl_use_sim.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/gz_bridge.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/joystick.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/main.rviz
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/map.rviz
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/mapper_params_online_async.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/my_controllers.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/nav2_params.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/twist_mux.yaml
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/config/view_bot.rviz
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/camera.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/depth_camera.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/face.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/gazebo_control.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/inertial_macros.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/lidar.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/robot.urdf.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/robot_core.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/description/ros2_control.xacro
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/ball_tracker.launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/camera.launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/joystick.launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/launch_robot.launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/launch_sim.launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/localization_launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/navigation_launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/online_async_launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/rplidar.launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/launch/rsp.launch.py
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/worlds/empty.world
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/worlds/obstacles.world
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/package_run_dependencies/articubot_one
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/parent_prefix_path/articubot_one
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/path.sh
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/environment/path.dsv
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.bash
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.sh
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.zsh
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/local_setup.dsv
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.dsv
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/ament_index/resource_index/packages/articubot_one
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/cmake/articubot_oneConfig.cmake
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/cmake/articubot_oneConfig-version.cmake
-- Symlinking: /home/<USER>/articubot_one/dev_ws/install/articubot_one/share/articubot_one/package.xml
