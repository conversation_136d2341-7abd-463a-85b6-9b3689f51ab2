{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-bb45dff0cd83a5fab788.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "articubot_one", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "articubot_one_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-articubot_one_uninstall-1b4532e6982618179a0d.json", "name": "articubot_one_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-91f85df55b8d69f8464f.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/articubot_one/dev_ws/build/articubot_one", "source": "/home/<USER>/articubot_one/dev_ws/src"}, "version": {"major": 2, "minor": 6}}