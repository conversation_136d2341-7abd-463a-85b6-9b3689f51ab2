<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" >

    <joint name="face_joint" type="fixed">
        <parent link="chassis"/>
        <child link="face_link"/>
        <origin xyz="${chassis_length} 0 ${chassis_height/2}" rpy="0 0 0"/>
    </joint>

    <link name="face_link">
        <visual>
            <origin xyz="0 0.05 0.01" rpy="0 ${pi/2} 0"/>
            <geometry>
                <cylinder radius="0.01" length="0.002"/>
            </geometry>
            <material name="black"/>
        </visual>

        <visual>
            <origin xyz="0 -0.05 0.01" rpy="0 ${pi/2} 0"/>
            <geometry>
                <cylinder radius="0.01" length="0.002"/>
            </geometry>
            <material name="black"/>
        </visual>


        <visual>
            <origin xyz="-0.011 0 -0.00" rpy="0 ${1.5} 0"/>
            <geometry>
                <cylinder radius="0.05" length="0.02"/>
            </geometry>
            <material name="red"/>
        </visual>
    </link>

    <gazebo reference="face_link">
        <material>Gazebo/Black</material>
    </gazebo>


</robot>