<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

    <gazebo>
        <plugin name="gz::sim::systems::DiffDrive" filename="gz-sim-diff-drive-system">

            <!-- Wheel Information -->
            <left_joint>left_wheel_joint</left_joint>
            <right_joint>right_wheel_joint</right_joint>
            <wheel_separation>0.297</wheel_separation>
            <wheel_radius>0.033</wheel_radius>


            <!-- Limits -->
            <!-- <max_wheel_torque>200</max_wheel_torque> -->
            <max_linear_acceleration>0.33</max_linear_acceleration>

            <!-- Input -->
            <topic>cmd_vel</topic>

            
            <!-- Output -->
            <frame_id>odom</frame_id>
            <child_frame_id>base_link</child_frame_id>
            <odom_topic>odom</odom_topic>
            <odom_publisher_frequency>30</odom_publisher_frequency>
            
            <tf_topic>/tf</tf_topic>

        </plugin>


        <plugin filename="gz-sim-joint-state-publisher-system"
            name="gz::sim::systems::JointStatePublisher">
            <topic>joint_states</topic>
            <joint_name>left_wheel_joint</joint_name>
            <joint_name>right_wheel_joint</joint_name>
        </plugin>
    </gazebo>


</robot>