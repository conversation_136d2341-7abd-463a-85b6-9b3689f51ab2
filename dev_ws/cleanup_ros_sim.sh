#!/bin/bash

# This script cleans up lingering ROS 2 and Gazebo processes.
# Use with caution, especially if you have other ROS 2 nodes running.

echo "Attempting to clean up lingering ROS 2 and Gazebo processes..."

# List of process names or parts of command lines to target
# These are derived from your 'ps -ef | grep ros' output
PROCESSES_TO_KILL=(
    "robot_state_publisher"
    "joy_node"
    "teleop_node" # Based on previous output, though not in the current 'grep ros' list
    "twist_stamper"
    "twist_mux" # Based on previous output, though it died
    "gzserver" # Gazebo server, typically runs as gzserver or gazebo
    "gzclient" # Gazebo GUI client, typically runs as gzclient or gazebo
    "gazebo" # General Gazebo process, can be server or client
    "ros_gz_sim" # The ROS-Gazebo bridge
    "controller_manager" # The ROS 2 controller manager
    "spawner" # For spawning controllers
    "parameter_bridge" # For ros_gz_bridge
    "image_bridge" # For ros_gz_bridge
)

# Function to kill processes by name
kill_process_by_name() {
    local process_name="$1"
    echo "Searching for processes containing: $process_name"
    # Use pgrep to find PIDs and then kill them
    # -f: search the full command line
    # -i: ignore case
    # -a: list full command line (for debugging)
    pids=$(pgrep -f "$process_name")

    if [ -n "$pids" ]; then
        echo "Found PIDs for '$process_name': $pids"
        for pid in $pids; do
            # Exclude the grep process itself
            if [[ "$pid" != "$$" && "$(ps -p $pid -o command=)" != *"grep $process_name"* ]]; then
                echo "Killing PID $pid: $(ps -p $pid -o command=)"
                kill -SIGTERM "$pid" # Try polite termination first
                sleep 0.5 # Give it a moment to terminate
                if kill -0 "$pid" 2>/dev/null; then # Check if process is still running
                    echo "PID $pid still alive, sending SIGKILL."
                    kill -SIGKILL "$pid" # Force kill if necessary
                fi
            fi
        done
    else
        echo "No processes found for '$process_name'."
    fi
}

# Iterate through the list and kill processes
for proc in "${PROCESSES_TO_KILL[@]}"; do
    kill_process_by_name "$proc"
done

echo "Cleanup attempt finished."

# Optional: Verify remaining ROS processes
echo ""
echo "Verifying remaining ROS processes:"
ps -ef | grep -E "ros|gazebo|gzclient|gzserver" | grep -v "grep"
